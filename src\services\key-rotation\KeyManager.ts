import { eq, and, sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import type { DatabaseInstance } from '../../server/types';
import { keys, usageLogs } from '../../app/schemas/key-rotation';
import type { Key, NewKey, NewUsageLog } from '../../app/schemas/key-rotation';
import type {
  AcquireOptions,
  AcquireResult,
  PoolStatus
} from '../../app/types/key-rotation';
import {
  KeyExhaustionError,
  ServiceNotFoundError
} from '../../app/types/key-rotation';
import { KeyRotationCache } from './KeyRotationCache';
import { KeySelectionEngine } from './KeySelectionEngine';

/**
 * 密钥管理器
 * 负责密钥的获取、释放和状态管理
 */
export class KeyManager {
  constructor(
    private db: DatabaseInstance,
    private cache: KeyRotationCache,
    private selectionEngine: KeySelectionEngine
  ) {}

  /**
   * 获取密钥（核心接口）
   */
  async acquireKey(serviceId: string, options?: AcquireOptions): Promise<AcquireResult> {
    const startTime = Date.now();

    try {
      // 1. 从缓存获取服务配置
      const service = await this.cache.getService(serviceId);
      if (!service || !service.is_active) {
        return {
          success: false,
          error: 'SERVICE_NOT_FOUND',
          message: 'Service not found or inactive',
          metadata: {
            service_id: serviceId,
            algorithm_used: 'none',
            pool_status: {
              total_keys: 0,
              active_keys: 0,
              exhausted_keys: 0,
              invalid_keys: 0,
              pool_health: 'critical'
            },
            response_time_ms: Date.now() - startTime
          }
        };
      }

      // 2. 原子性获取密钥
      const algorithm = options?.algorithm || service.config.rotation_strategy.selection_algorithm;
      const key = await this.acquireKeyAtomic(serviceId, algorithm);

      if (!key) {
        const poolStatus = await this.cache.getPoolStatus(serviceId);
        return {
          success: false,
          error: 'NO_AVAILABLE_KEYS',
          message: 'All keys have been exhausted',
          metadata: {
            service_id: serviceId,
            algorithm_used: algorithm,
            pool_status: poolStatus,
            response_time_ms: Date.now() - startTime
          }
        };
      }

      // 3. 获取池状态
      const poolStatus = await this.cache.getPoolStatus(serviceId);

      // 4. 返回成功结果
      return {
        success: true,
        data: {
          key_id: key.id,
          key_value: key.key_value,
          usage_count: key.usage_count,
          remaining_usage: key.max_usage - key.usage_count,
          expires_in_uses: key.max_usage - key.usage_count
        },
        metadata: {
          service_id: serviceId,
          algorithm_used: algorithm,
          pool_status: poolStatus,
          response_time_ms: Date.now() - startTime
        }
      };

    } catch (error) {
      console.error('Key acquisition failed:', {
        serviceId,
        error: error instanceof Error ? error.message : 'Unknown error',
        response_time_ms: Date.now() - startTime
      });

      return {
        success: false,
        error: 'SYSTEM_ERROR',
        message: 'Internal server error',
        metadata: {
          service_id: serviceId,
          algorithm_used: 'none',
          pool_status: {
            total_keys: 0,
            active_keys: 0,
            exhausted_keys: 0,
            invalid_keys: 0,
            pool_health: 'critical'
          },
          response_time_ms: Date.now() - startTime
        }
      };
    }
  }

  /**
   * 原子性密钥获取（使用事务确保并发安全）
   */
  private async acquireKeyAtomic(serviceId: string, algorithm: string): Promise<Key | null> {
    return await this.db.transaction(async (tx) => {
      // 查询可用密钥（限制候选集大小提升性能）
      const candidateKeys = await tx
        .select()
        .from(keys)
        .where(
          and(
            eq(keys.service_id, serviceId),
            eq(keys.status, 'active'),
            sql`usage_count < max_usage`
          )
        )
        .orderBy(keys.usage_count)
        .limit(10); // 限制候选集大小

      if (candidateKeys.length === 0) {
        return null;
      }

      // 应用选择算法
      const selectedKey = this.selectionEngine.select(candidateKeys, algorithm, serviceId);
      if (!selectedKey) {
        return null;
      }

      // 原子更新使用计数
      const newUsageCount = selectedKey.usage_count + 1;
      await tx
        .update(keys)
        .set({
          usage_count: newUsageCount,
          last_used_at: new Date().toISOString()
        })
        .where(eq(keys.id, selectedKey.id));

      // 检查是否需要标记为耗尽
      if (newUsageCount >= selectedKey.max_usage) {
        await tx
          .update(keys)
          .set({ status: 'exhausted' })
          .where(eq(keys.id, selectedKey.id));
      }

      // 记录使用日志（暂时禁用以避免SQLite锁定问题）
      // this.logUsageAsync(selectedKey.id, serviceId, {
      //   algorithm_used: algorithm,
      //   usage_count: newUsageCount
      // });

      // 失效相关缓存
      this.cache.invalidateServiceCache(serviceId);

      return {
        ...selectedKey,
        usage_count: newUsageCount
      };
    });
  }

  /**
   * 批量添加密钥
   */
  async addKeys(serviceId: string, keyData: Array<{
    key_value: string;
    max_usage?: number;
    priority?: number;
  }>): Promise<Key[]> {
    // 获取服务配置以确定默认max_usage
    const service = await this.cache.getService(serviceId);
    if (!service) {
      throw new ServiceNotFoundError('Service not found');
    }

    const defaultMaxUsage = service.config.rotation_strategy.max_usage;

    const newKeys: NewKey[] = keyData.map(data => ({
      id: uuidv4(),
      service_id: serviceId,
      key_value: data.key_value,
      status: 'active',
      usage_count: 0,
      max_usage: data.max_usage || defaultMaxUsage,
      priority: data.priority || 1.0,
      created_at: new Date().toISOString(),
      last_used_at: null
    }));

    const result = await this.db
      .insert(keys)
      .values(newKeys)
      .returning();

    // 失效缓存
    this.cache.invalidateServiceCache(serviceId);

    return result;
  }

  /**
   * 标记密钥为无效
   */
  async invalidateKey(keyId: string): Promise<boolean> {
    const result = await this.db
      .update(keys)
      .set({ 
        status: 'invalid',
        last_used_at: new Date().toISOString()
      })
      .where(eq(keys.id, keyId))
      .returning();

    if (result.length > 0) {
      const key = result[0];
      // 失效相关缓存
      this.cache.invalidateServiceCache(key.service_id);
      return true;
    }

    return false;
  }

  /**
   * 获取密钥详情
   */
  async getKey(keyId: string): Promise<Key | null> {
    const result = await this.db
      .select()
      .from(keys)
      .where(eq(keys.id, keyId))
      .limit(1);

    return result[0] || null;
  }

  /**
   * 获取服务的所有密钥
   */
  async getServiceKeys(serviceId: string, options?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<Key[]> {
    let query = this.db
      .select()
      .from(keys)
      .where(eq(keys.service_id, serviceId));

    if (options?.status) {
      query = query.where(and(eq(keys.service_id, serviceId), eq(keys.status, options.status as any)));
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.offset(options.offset);
    }

    return await query.orderBy(keys.created_at);
  }

  /**
   * 更新密钥
   */
  async updateKey(serviceId: string, keyId: string, updates: {
    status?: string;
    priority?: number;
    max_usage?: number;
  }): Promise<Key | null> {
    const updateData: any = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    const result = await this.db
      .update(keys)
      .set(updateData)
      .where(and(eq(keys.id, keyId), eq(keys.service_id, serviceId)))
      .returning();

    const updatedKey = result[0] || null;

    if (updatedKey) {
      // 清理相关缓存
      this.cache.invalidateServiceCache(serviceId);
    }

    return updatedKey;
  }

  /**
   * 删除密钥
   */
  async deleteKey(serviceId: string, keyId: string): Promise<boolean> {
    const result = await this.db
      .delete(keys)
      .where(and(eq(keys.id, keyId), eq(keys.service_id, serviceId)))
      .returning();

    const deleted = result.length > 0;

    if (deleted) {
      // 清理相关缓存
      this.cache.invalidateServiceCache(serviceId);
    }

    return deleted;
  }

  /**
   * 释放密钥
   */
  async releaseKey(
    serviceId: string,
    keyId: string,
    isValid: boolean = true,
    metadata?: any
  ): Promise<{
    success: boolean;
    error?: string;
    message?: string;
    new_status?: string;
    usage_count?: number;
    is_exhausted?: boolean;
    metadata?: any;
  }> {
    try {
      // 获取密钥当前状态
      const key = await this.db
        .select()
        .from(keys)
        .where(and(eq(keys.id, keyId), eq(keys.service_id, serviceId)))
        .limit(1);

      if (key.length === 0) {
        return {
          success: false,
          error: 'KEY_NOT_FOUND',
          message: 'Key not found'
        };
      }

      const currentKey = key[0];
      let newStatus = currentKey.status;

      // 如果密钥无效，标记为失效
      if (!isValid) {
        newStatus = 'invalid';
      } else if (currentKey.usage_count >= currentKey.max_usage) {
        newStatus = 'exhausted';
      }

      // 更新密钥状态
      const result = await this.db
        .update(keys)
        .set({
          status: newStatus as any,
          last_used_at: new Date().toISOString()
        })
        .where(and(eq(keys.id, keyId), eq(keys.service_id, serviceId)))
        .returning();

      const updatedKey = result[0];

      // 记录使用日志
      this.logUsageAsync(keyId, serviceId, metadata);

      // 清理缓存
      this.cache.invalidateServiceCache(serviceId);

      return {
        success: true,
        new_status: newStatus,
        usage_count: updatedKey.usage_count,
        is_exhausted: newStatus === 'exhausted',
        metadata: {
          released_at: new Date().toISOString(),
          was_valid: isValid
        }
      };
    } catch (error) {
      console.error('Release key error:', error);
      return {
        success: false,
        error: 'RELEASE_FAILED',
        message: error instanceof Error ? error.message : 'Failed to release key'
      };
    }
  }

  /**
   * 异步记录使用日志
   */
  private async logUsageAsync(keyId: string, serviceId: string, metadata?: any): Promise<void> {
    try {
      const logEntry: NewUsageLog = {
        id: uuidv4(),
        key_id: keyId,
        service_id: serviceId,
        timestamp: new Date().toISOString(),
        metadata: metadata ? JSON.stringify(metadata) : null
      };

      // 异步插入，不等待结果
      this.db.insert(usageLogs).values(logEntry).execute().catch(error => {
        console.error('Failed to log usage:', error);
      });
    } catch (error) {
      console.error('Error creating usage log:', error);
    }
  }
}
