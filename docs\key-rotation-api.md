# Key Rotation Service API Documentation

## Overview

The Key Rotation Service has been refactored to use the Hono route builder pattern and integrates seamlessly with the Next.js-style file-based routing system. All endpoints follow RESTful conventions and return consistent JSON responses.

## Architecture

### File-Based Routing Structure
```
src/app/api/
├── services/
│   ├── route.ts                           # GET, POST /api/services
│   └── [id]/
│       ├── route.ts                       # GET, PUT, DELETE /api/services/{id}
│       ├── stats/route.ts                 # GET /api/services/{id}/stats
│       └── keys/
│           ├── route.ts                   # GET, POST /api/services/{id}/keys
│           ├── acquire/route.ts           # POST /api/services/{id}/keys/acquire
│           └── [keyId]/
│               ├── route.ts               # GET, PUT, DELETE /api/services/{id}/keys/{keyId}
│               └── release/route.ts       # POST /api/services/{id}/keys/{keyId}/release
└── key-rotation/
    └── test/route.ts                      # GET, POST /api/key-rotation/test
```

### Route Builder Integration

All endpoints use the `HonoApiRouteBuilder` with custom handlers:

```typescript
const serviceRoutes = new HonoApiRouteBuilder()
  .table(services)
  .primaryKey('id')
  .schema({ create: CreateServiceSchema, update: UpdateServiceSchema })
  .customHandler('create', async (c: Context) => { /* custom logic */ })
  .customHandler('list', async (c: Context) => { /* custom logic */ })
  .build();
```

## API Endpoints

### Service Management

#### List Services
- **GET** `/api/services`
- **Query Parameters:**
  - `active_only` (boolean): Filter active services only
  - `limit` (number): Limit results
  - `offset` (number): Pagination offset

#### Create Service
- **POST** `/api/services`
- **Body:**
```json
{
  "name": "string",
  "description": "string",
  "rotation_strategy": "usage_based|time_based|frequency_based",
  "max_usage_per_key": "number",
  "key_selection_algorithm": "round_robin|priority|random"
}
```

#### Get Service Details
- **GET** `/api/services/{id}`

#### Update Service
- **PUT** `/api/services/{id}`
- **Body:** Same as create, all fields optional

#### Delete Service
- **DELETE** `/api/services/{id}`

#### Get Service Statistics
- **GET** `/api/services/{id}/stats`

### Key Management

#### List Service Keys
- **GET** `/api/services/{id}/keys`
- **Query Parameters:**
  - `status` (string): Filter by key status
  - `limit` (number): Limit results
  - `offset` (number): Pagination offset

#### Add Keys to Service
- **POST** `/api/services/{id}/keys`
- **Body (Single Key):**
```json
{
  "key_value": "string",
  "priority": "number",
  "max_usage": "number"
}
```
- **Body (Batch):**
```json
{
  "keys": [
    { "key_value": "string", "priority": "number" },
    { "key_value": "string", "priority": "number" }
  ]
}
```

#### Acquire Key
- **POST** `/api/services/{id}/keys/acquire`
- **Body (Optional):**
```json
{
  "algorithm": "round_robin|priority|random",
  "metadata": {}
}
```

#### Get Key Details
- **GET** `/api/services/{id}/keys/{keyId}`

#### Update Key
- **PUT** `/api/services/{id}/keys/{keyId}`
- **Body:**
```json
{
  "status": "string",
  "priority": "number",
  "max_usage": "number"
}
```

#### Delete Key
- **DELETE** `/api/services/{id}/keys/{keyId}`

#### Release Key
- **POST** `/api/services/{id}/keys/{keyId}/release`
- **Body:**
```json
{
  "is_valid": "boolean",
  "metadata": {}
}
```

### Testing

#### System Status
- **GET** `/api/key-rotation/test`
- Returns system status and available endpoints

#### Run System Tests
- **POST** `/api/key-rotation/test`
- Runs comprehensive system tests

## Response Format

All endpoints return consistent JSON responses:

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "Optional success message",
  "metadata": {}
}
```

### Error Response
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message"
}
```

## Key Features

### 1. **Consistent Architecture**
- All routes use the same Hono route builder pattern
- Consistent error handling and response formats
- Proper HTTP status codes

### 2. **Enhanced Route Builder**
- Added `customHandler()` method for complex business logic
- Supports both CRUD operations and custom endpoints
- Maintains compatibility with existing patterns

### 3. **Comprehensive API Coverage**
- Full CRUD operations for services and keys
- Advanced features like key acquisition and release
- Statistics and monitoring endpoints

### 4. **File-Based Routing**
- Zero-configuration route registration
- Next.js-style file conventions
- Automatic route discovery and registration

### 5. **Testing Integration**
- Built-in test endpoints
- Comprehensive system testing
- Easy verification of functionality

## Migration Notes

### Removed Components
- Custom API endpoint definitions
- Manual route registration
- Inconsistent response formats

### Enhanced Components
- `HonoApiRouteBuilder` with custom handler support
- `KeyManager` with additional CRUD methods
- Comprehensive error handling

### New Features
- File-based routing integration
- Standardized response formats
- Built-in testing endpoints
- Enhanced documentation

## Usage Examples

### Basic Service Creation and Key Management
```bash
# Create a service
curl -X POST http://localhost:4000/api/services \
  -H "Content-Type: application/json" \
  -d '{"name": "my-service", "rotation_strategy": "usage_based"}'

# Add keys
curl -X POST http://localhost:4000/api/services/{id}/keys \
  -H "Content-Type: application/json" \
  -d '{"keys": [{"key_value": "key1"}, {"key_value": "key2"}]}'

# Acquire a key
curl -X POST http://localhost:4000/api/services/{id}/keys/acquire

# Release a key
curl -X POST http://localhost:4000/api/services/{id}/keys/{keyId}/release \
  -H "Content-Type: application/json" \
  -d '{"is_valid": true}'
```

This refactored system provides a clean, consistent, and scalable API for key rotation management while following established architectural patterns.
