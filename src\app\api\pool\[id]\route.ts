/**
 * 单个服务管理 API
 * 路径: /api/services/[id]
 * 支持的方法: GET, PUT, DELETE
 */

import type { Context } from 'hono';
import { HonoApiRouteBuilder } from '@/server/builders/hono-route-builder';
import { services } from '@/app/schemas/key-rotation';
import { UpdateServiceSchema } from '@/app/types/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

// 创建单个服务路由构建器
const serviceDetailRoutes = new HonoApiRouteBuilder()
  .table(services)
  .primaryKey('id')
  .schema({
    update: UpdateServiceSchema
  })
  .customHandler('get', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const service = await keyRotationServices.serviceManager.getService(serviceId);
      
      if (!service) {
        return c.json({
          success: false,
          error: 'Service not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        data: service
      });
    } catch (error) {
      console.error('Get service error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service'
      }, 500);
    }
  })
  .customHandler('update', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const body = await c.req.json();
      const validatedData = UpdateServiceSchema.parse(body);
      
      const service = await keyRotationServices.serviceManager.updateService(serviceId, validatedData);
      
      if (!service) {
        return c.json({
          success: false,
          error: 'Service not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        data: service
      });
    } catch (error) {
      console.error('Update service error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update service'
      }, 400);
    }
  })
  .customHandler('delete', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const deleted = await keyRotationServices.serviceManager.deleteService(serviceId);
      
      if (!deleted) {
        return c.json({
          success: false,
          error: 'Service not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        message: 'Service deleted successfully'
      });
    } catch (error) {
      console.error('Delete service error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete service'
      }, 500);
    }
  })
  .build();

/**
 * GET /api/services/[id] - 获取服务详情
 */
export async function GET(c: Context) {
  return serviceDetailRoutes.GET(c);
}

/**
 * PUT /api/services/[id] - 更新服务
 */
export async function PUT(c: Context) {
  return serviceDetailRoutes.PUT(c);
}

/**
 * DELETE /api/services/[id] - 删除服务
 */
export async function DELETE(c: Context) {
  return serviceDetailRoutes.DELETE(c);
}
