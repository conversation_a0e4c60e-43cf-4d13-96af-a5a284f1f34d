import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import type { DatabaseInstance } from '../../server/types';
import { services } from '../../app/schemas/key-rotation';
import type {
  Service,
  NewService,
  ServiceConfig
} from '../../app/schemas/key-rotation';
import type {
  CreateServiceRequest,
  UpdateServiceRequest
} from '../../app/types/key-rotation';
import { ValidationError } from '../../app/types/key-rotation';
import { KeyRotationCache } from './KeyRotationCache';

/**
 * 服务管理器
 * 负责服务的CRUD操作和配置管理
 */
export class ServiceManager {
  constructor(
    private db: DatabaseInstance,
    private cache: KeyRotationCache
  ) {}

  /**
   * 创建新服务
   */
  async createService(data: CreateServiceRequest): Promise<Service> {
    // 验证配置的有效性
    this.validateServiceConfig(data.config);

    const service: NewService = {
      id: uuidv4(),
      name: data.name,
      description: data.description,
      config: data.config,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    try {
      const result = await this.db
        .insert(services)
        .values(service)
        .returning();

      const createdService = result[0];
      
      // 缓存新创建的服务
      this.cache.set(`service:${createdService.id}`, createdService);

      return createdService;
    } catch (error) {
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        throw new ValidationError(`Service name '${data.name}' already exists`);
      }
      throw error;
    }
  }

  /**
   * 获取服务详情
   */
  async getService(serviceId: string): Promise<Service | null> {
    return await this.cache.getService(serviceId);
  }

  /**
   * 获取所有服务列表
   */
  async listServices(options: {
    active_only?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<Service[]> {
    let query = this.db.select().from(services);

    if (options.active_only) {
      query = query.where(eq(services.is_active, true));
    }

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.offset(options.offset);
    }

    return await query.orderBy(services.created_at);
  }

  /**
   * 更新服务
   */
  async updateService(serviceId: string, data: UpdateServiceRequest): Promise<Service | null> {
    // 如果更新配置，验证其有效性
    if (data.config) {
      this.validateServiceConfig(data.config as ServiceConfig);
    }

    const updateData: Partial<NewService> = {
      ...data,
      updated_at: new Date().toISOString()
    };

    try {
      const result = await this.db
        .update(services)
        .set(updateData)
        .where(eq(services.id, serviceId))
        .returning();

      const updatedService = result[0] || null;

      if (updatedService) {
        // 更新缓存
        this.cache.set(`service:${serviceId}`, updatedService);
        // 失效相关缓存
        this.cache.invalidateServiceCache(serviceId);
      }

      return updatedService;
    } catch (error) {
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        throw new ValidationError(`Service name '${data.name}' already exists`);
      }
      throw error;
    }
  }

  /**
   * 删除服务
   */
  async deleteService(serviceId: string): Promise<boolean> {
    const result = await this.db
      .delete(services)
      .where(eq(services.id, serviceId))
      .returning();

    const deleted = result.length > 0;

    if (deleted) {
      // 清理缓存
      this.cache.invalidateServiceCache(serviceId);
    }

    return deleted;
  }

  /**
   * 激活/停用服务
   */
  async toggleServiceStatus(serviceId: string, isActive: boolean): Promise<Service | null> {
    const result = await this.db
      .update(services)
      .set({ 
        is_active: isActive,
        updated_at: new Date().toISOString()
      })
      .where(eq(services.id, serviceId))
      .returning();

    const updatedService = result[0] || null;

    if (updatedService) {
      // 更新缓存
      this.cache.set(`service:${serviceId}`, updatedService);
      // 失效相关缓存
      this.cache.invalidateServiceCache(serviceId);
    }

    return updatedService;
  }

  /**
   * 验证服务配置
   */
  private validateServiceConfig(config: ServiceConfig): void {
    // 验证轮换策略
    if (config.rotation_strategy.max_usage <= 0) {
      throw new ValidationError('max_usage must be greater than 0');
    }

    if (config.rotation_strategy.max_usage > 10000) {
      throw new ValidationError('max_usage cannot exceed 10000');
    }

    const validAlgorithms = ['round_robin', 'random', 'weighted', 'least_used'];
    if (!validAlgorithms.includes(config.rotation_strategy.selection_algorithm)) {
      throw new ValidationError(`Invalid selection algorithm. Must be one of: ${validAlgorithms.join(', ')}`);
    }

    // 验证池配置
    if (config.pool_config.min_active_keys <= 0) {
      throw new ValidationError('min_active_keys must be greater than 0');
    }

    // 验证缓存配置
    if (config.cache_config.cache_ttl_seconds <= 0 || config.cache_config.cache_ttl_seconds > 3600) {
      throw new ValidationError('cache_ttl_seconds must be between 1 and 3600');
    }
  }

  /**
   * 获取服务统计信息
   */
  async getServiceStats(serviceId: string): Promise<{
    service: Service | null;
    pool_status: any;
    total_usage: number;
  }> {
    const [service, poolStatus] = await Promise.all([
      this.getService(serviceId),
      this.cache.getPoolStatus(serviceId)
    ]);

    // 计算总使用次数（简化版本，实际可能需要从usage_logs计算）
    const totalUsage = poolStatus.total_keys * 100; // 临时计算

    return {
      service,
      pool_status: poolStatus,
      total_usage: totalUsage
    };
  }
}
