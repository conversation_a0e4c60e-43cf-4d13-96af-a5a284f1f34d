// 导出所有密钥轮换服务类
export { ServiceManager } from './ServiceManager';
export { KeyManager } from './KeyManager';
export { KeySelectionEngine } from './KeySelectionEngine';
export { KeyRotationCache } from './KeyRotationCache';

// 导出工厂函数
import { db } from '../../app/db';
import { ServiceManager } from './ServiceManager';
import { KeyManager } from './KeyManager';
import { KeySelectionEngine } from './KeySelectionEngine';
import { KeyRotationCache } from './KeyRotationCache';

/**
 * 创建密钥轮换服务实例的工厂函数
 */
export function createKeyRotationServices() {
  // 创建缓存实例
  const cache = new KeyRotationCache(db, {
    maxSize: 1000,
    defaultTTL: 30000 // 30秒
  });

  // 创建选择引擎
  const selectionEngine = new KeySelectionEngine();

  // 创建管理器实例
  const serviceManager = new ServiceManager(db, cache);
  const keyManager = new KeyManager(db, cache, selectionEngine);

  return {
    serviceManager,
    keyManager,
    cache,
    selectionEngine
  };
}

// 创建单例实例（用于API路由）
export const keyRotationServices = createKeyRotationServices();
