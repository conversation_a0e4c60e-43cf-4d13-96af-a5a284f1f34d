/**
 * 密钥释放 API
 * 路径: /api/services/[id]/keys/[keyId]/release
 * 支持的方法: POST
 */

import type { Context } from 'hono';
import { ReleaseKeySchema } from '@/app/types/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

/**
 * POST /api/services/[id]/keys/[keyId]/release - 释放密钥
 */
export async function POST(c: Context) {
  try {
    const serviceId = c.req.param('id');
    const keyId = c.req.param('keyId');
    const body = await c.req.json().catch(() => ({}));
    
    // 验证请求数据
    const validatedData = ReleaseKeySchema.parse(body);
    
    const result = await keyRotationServices.keyManager.releaseKey(
      serviceId,
      keyId,
      validatedData.is_valid,
      validatedData.metadata
    );
    
    if (!result.success) {
      return c.json({
        success: false,
        error: result.error,
        message: result.message
      }, result.error === 'KEY_NOT_FOUND' ? 404 : 400);
    }
    
    return c.json({
      success: true,
      data: {
        key_id: keyId,
        new_status: result.new_status,
        usage_count: result.usage_count,
        is_exhausted: result.is_exhausted
      },
      metadata: result.metadata
    });
  } catch (error) {
    console.error('Release key error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to release key'
    }, 500);
  }
}
