/**
 * 服务管理 API
 * 路径: /api/services
 * 支持的方法: GET, POST
 */

import { HonoApiRouteBuilder } from '@/server/builders/hono-route-builder';
import { services } from '@/app/schemas/key-rotation';
import type { ProcessedApiContext } from '@/server/types';
import { CreateServiceSchema, UpdateServiceSchema } from '@/app/types/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

// 创建服务管理路由构建器
const serviceRoutes = new HonoApiRouteBuilder()
  .resource('pool', services)
  .primaryKey('id')
  .schema({
    create: CreateServiceSchema,
    update: UpdateServiceSchema
  })
  .post(async (c: ProcessedApiContext) => {
    try {
      const body = await c.body;
      const validatedData = CreateServiceSchema.parse(body);
      
      const service = await keyRotationServices.serviceManager.createService(validatedData);
      
      return c.json({
        success: true,
        data: service
      }, 201);
    } catch (error) {
      console.error('Create service error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create service'
      }, 400);
    }
  })
  .get(async (c: Context) => {
    try {
      const query = c.req.query();
      const options = {
        active_only: query.active_only === 'true',
        limit: query.limit ? parseInt(query.limit) : undefined,
        offset: query.offset ? parseInt(query.offset) : undefined
      };
      
      const services = await keyRotationServices.serviceManager.listServices(options);
      
      return c.json({
        success: true,
        data: services,
        pagination: {
          limit: options.limit,
          offset: options.offset,
          total: services.length
        }
      });
    } catch (error) {
      console.error('List services error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list services'
      }, 500);
    }
  })
  .build();

/**
 * GET /api/services - 获取服务列表
 */
export async function GET(c: Context) {
  return serviceRoutes.GET(c);
}

/**
 * POST /api/services - 创建新服务
 */
export async function POST(c: Context) {
  return serviceRoutes.POST(c);
}
