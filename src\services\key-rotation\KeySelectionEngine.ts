import type { Key } from '../../app/schemas/key-rotation';

/**
 * 密钥选择引擎
 * 实现多种密钥选择算法
 */
export class KeySelectionEngine {
  private lastUsedIndexMap = new Map<string, number>();

  /**
   * 根据算法选择密钥
   */
  select(keys: Key[], algorithm: string, serviceId?: string): Key | null {
    const availableKeys = keys.filter(k => 
      k.status === 'active' && k.usage_count < k.max_usage
    );

    if (availableKeys.length === 0) {
      return null;
    }

    switch (algorithm) {
      case 'round_robin':
        return this.selectRoundRobin(availableKeys, serviceId || '');
      case 'random':
        return this.selectRandom(availableKeys);
      case 'weighted':
        return this.selectWeighted(availableKeys);
      case 'least_used':
        return this.selectLeastUsed(availableKeys);
      default:
        return this.selectRoundRobin(availableKeys, serviceId || '');
    }
  }

  /**
   * 轮询算法 - 适用于均匀分配场景
   * 时间复杂度: O(1)，空间复杂度: O(1)
   */
  private selectRoundRobin(keys: Key[], serviceId: string): Key | null {
    if (keys.length === 0) return null;

    const lastUsedIndex = this.lastUsedIndexMap.get(serviceId) || 0;
    const nextIndex = (lastUsedIndex + 1) % keys.length;
    
    // 更新最后使用的索引
    this.lastUsedIndexMap.set(serviceId, nextIndex);
    
    return keys[nextIndex];
  }

  /**
   * 随机算法 - 适用于简单负载分散
   * 时间复杂度: O(1)
   */
  private selectRandom(keys: Key[]): Key | null {
    if (keys.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * keys.length);
    return keys[randomIndex];
  }

  /**
   * 最少使用算法 - 适用于平衡负载场景
   * 时间复杂度: O(n)，但通过索引优化可达O(log n)
   */
  private selectLeastUsed(keys: Key[]): Key | null {
    if (keys.length === 0) return null;

    return keys.reduce((least, current) => 
      current.usage_count < least.usage_count ? current : least
    );
  }

  /**
   * 权重算法 - 适用于密钥质量不等场景
   * 时间复杂度: O(n)
   */
  private selectWeighted(keys: Key[]): Key | null {
    if (keys.length === 0) return null;

    // 计算总权重
    const totalWeight = keys.reduce((sum, key) => sum + key.priority, 0);
    
    if (totalWeight <= 0) {
      // 如果没有权重，回退到随机选择
      return this.selectRandom(keys);
    }

    // 生成随机数
    const random = Math.random() * totalWeight;
    
    // 根据权重选择
    let currentWeight = 0;
    for (const key of keys) {
      currentWeight += key.priority;
      if (random <= currentWeight) {
        return key;
      }
    }

    // 兜底返回第一个
    return keys[0];
  }

  /**
   * 清理缓存的轮询索引
   */
  clearCache(serviceId?: string): void {
    if (serviceId) {
      this.lastUsedIndexMap.delete(serviceId);
    } else {
      this.lastUsedIndexMap.clear();
    }
  }

  /**
   * 获取缓存状态（用于调试）
   */
  getCacheStatus(): Record<string, number> {
    return Object.fromEntries(this.lastUsedIndexMap);
  }
}
