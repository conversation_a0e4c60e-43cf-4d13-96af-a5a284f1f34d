import type { Context } from "hono";
import type { RequestInput, HttpMethod } from "../types";
import { BaseApiRouteBuilder } from "./base-route-builder";
import { ResponseUtils } from "../utils/response";

/**
 * Hono 框架的路由构建器
 * 继承基类，专门为 Hono 框架优化
 * 支持文件路由系统，返回 HTTP 方法处理器对象
 */
export class HonoApiRouteBuilder extends BaseApiRouteBuilder {

    private async handleRequest(c: Context): Promise<Response> {
        // Create RequestInput adapter for Hono
        const input: RequestInput = {
            method: c.req.method as HttpMethod,
            url: c.req.url,
            headers: c.req.raw.headers,
            params: c.req.param(),
            searchParams: new URL(c.req.url).searchParams,
            getBody: async () => {
                try {
                    return await c.req.json();
                } catch {
                    return {};
                }
            }
        };

        // Use the common request processing logic from base class
        return this.processRequest(input);
    }

    /**
     * 构建方法 - 返回 HTTP 方法处理器对象
     * 类似 Next.js API Routes 的导出格式，支持文件路由系统
     * @returns HTTP 方法处理器对象
     */
    build() {
        return {
            GET: this.handleRequest.bind(this),
            POST: this.handleRequest.bind(this),
            PUT: this.handleRequest.bind(this),
            DELETE: this.handleRequest.bind(this),
            PATCH: this.handleRequest.bind(this),
            OPTIONS: () => ResponseUtils.success(null),
        };
    }
}