/**
 * 密钥获取 API
 * 路径: /api/services/[id]/keys/acquire
 * 支持的方法: POST
 */

import type { Context } from 'hono';
import { AcquireKeySchema } from '@/app/types/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

/**
 * POST /api/services/[id]/keys/acquire - 获取可用密钥
 */
export async function POST(c: Context) {
  try {
    const serviceId = c.req.param('id');
    const body = await c.req.json().catch(() => ({}));
    
    // 验证请求数据（可选参数）
    const validatedData = AcquireKeySchema.parse(body);
    
    const result = await keyRotationServices.keyManager.acquireKey(serviceId, {
      algorithm: validatedData.algorithm,
      metadata: validatedData.metadata
    });
    
    if (!result.success) {
      return c.json({
        success: false,
        error: result.error,
        message: result.message,
        metadata: result.metadata
      }, result.error === 'SERVICE_NOT_FOUND' ? 404 : 400);
    }
    
    return c.json({
      success: true,
      data: {
        key_id: result.key_id,
        key_value: result.key_value,
        usage_count: result.usage_count,
        max_usage: result.max_usage,
        expires_at: result.expires_at
      },
      metadata: result.metadata
    });
  } catch (error) {
    console.error('Acquire key error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to acquire key'
    }, 500);
  }
}
