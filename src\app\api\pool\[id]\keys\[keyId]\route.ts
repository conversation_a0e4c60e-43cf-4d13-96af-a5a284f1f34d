/**
 * 单个密钥管理 API
 * 路径: /api/services/[id]/keys/[keyId]
 * 支持的方法: GET, PUT, DELETE
 */

import type { Context } from 'hono';
import { HonoApiRouteBuilder } from '@/server/builders/hono-route-builder';
import { keys } from '@/app/schemas/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

// 创建单个密钥路由构建器
const keyDetailRoutes = new HonoApiRouteBuilder()
  .table(keys)
  .primaryKey('id')
  .customHandler('get', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const keyId = c.req.param('keyId');
      
      const key = await keyRotationServices.keyManager.getKey(serviceId, keyId);
      
      if (!key) {
        return c.json({
          success: false,
          error: 'Key not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        data: key
      });
    } catch (error) {
      console.error('Get key error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get key'
      }, 500);
    }
  })
  .customHandler('update', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const keyId = c.req.param('keyId');
      const body = await c.req.json();
      
      // 只允许更新某些字段
      const allowedUpdates = {
        status: body.status,
        priority: body.priority,
        max_usage: body.max_usage
      };
      
      const key = await keyRotationServices.keyManager.updateKey(serviceId, keyId, allowedUpdates);
      
      if (!key) {
        return c.json({
          success: false,
          error: 'Key not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        data: key
      });
    } catch (error) {
      console.error('Update key error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update key'
      }, 400);
    }
  })
  .customHandler('delete', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const keyId = c.req.param('keyId');
      
      const deleted = await keyRotationServices.keyManager.deleteKey(serviceId, keyId);
      
      if (!deleted) {
        return c.json({
          success: false,
          error: 'Key not found'
        }, 404);
      }
      
      return c.json({
        success: true,
        message: 'Key deleted successfully'
      });
    } catch (error) {
      console.error('Delete key error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete key'
      }, 500);
    }
  })
  .build();

/**
 * GET /api/services/[id]/keys/[keyId] - 获取密钥详情
 */
export async function GET(c: Context) {
  return keyDetailRoutes.GET(c);
}

/**
 * PUT /api/services/[id]/keys/[keyId] - 更新密钥
 */
export async function PUT(c: Context) {
  return keyDetailRoutes.PUT(c);
}

/**
 * DELETE /api/services/[id]/keys/[keyId] - 删除密钥
 */
export async function DELETE(c: Context) {
  return keyDetailRoutes.DELETE(c);
}
