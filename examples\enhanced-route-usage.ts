// 中间件架构的积分系统使用示例
import { standardCredits, dataBasedCredits, batchCredits, tieredCredits, freeTierCredits } from "../src/server/middleware/credit-config-factory";
import { createCreditMiddleware } from "../src/server/middleware/credit-middleware";
import type { RouteCreditConfig } from "../src/server/middleware/credit-middleware";

/**
 * 示例 1: 标准 CRUD 积分配置
 */
export const standardCrudCredits: RouteCreditConfig = standardCredits({
  read: 1,
  write: 5,
  delete: 10,
  strategy: 'post',
  refundOnFailure: true,
});

/**
 * 示例 2: 基于数据量的动态积分配置
 */
export const dataBasedApiCredits: RouteCreditConfig = dataBasedCredits({
  baseRate: 2,
  perRecord: 0.1,
  maxCost: 50,
  method: 'GET',
});

/**
 * 示例 3: 批量操作积分配置
 */
export const batchOperationCredits: RouteCreditConfig = batchCredits({
  baseCost: 5,
  perOperationCost: 0.5,
  maxCost: 100,
  discount: 0.8,
});

/**
 * 示例 4: 分层定价积分配置
 */
export const tieredPricingCredits: RouteCreditConfig = tieredCredits({
  free: { read: 2, write: 10, delete: 20, strategy: 'pre' },
  premium: { read: 1, write: 3, delete: 5, strategy: 'post' },
  admin: { read: 0, write: 0, delete: 0 },
});

/**
 * 示例 5: 免费层限制配置
 */
export const freeTierLimitCredits: RouteCreditConfig = freeTierCredits();

/**
 * 示例 6: 复杂的动态积分配置
 */
export const complexAnalyticsCredits: RouteCreditConfig = {
  dynamic: async (context) => {
    const { startDate, endDate, groupBy } = context.req.query;

    let cost = 5; // 基础查询费用

    // 时间范围越大，费用越高
    if (startDate && endDate) {
      const startTime = new Date(startDate as string).getTime();
      const endTime = new Date(endDate as string).getTime();
      const days = Math.ceil((endTime - startTime) / (1000 * 60 * 60 * 24));
      cost += Math.min(days * 0.5, 20); // 最多额外 20 积分
    }

    // 分组查询额外费用
    if (groupBy) {
      cost += 10;
    }

    return {
      cost: Math.ceil(cost),
      deductionStrategy: 'post',
      refundOnFailure: true,
    };
  }
};

/**
 * 示例 7: 公开 API 积分配置（只对写操作收费）
 */
export const publicApiCredits: RouteCreditConfig = {
  methods: {
    POST: {
      cost: 3,
      deductionStrategy: 'pre',
      refundOnFailure: true,
    },
    PUT: {
      cost: 2,
      deductionStrategy: 'post',
    },
    DELETE: {
      cost: 5,
      deductionStrategy: 'pre',
      minimumBalance: 10,
    },
  }
};

/**
 * 示例 8: 路径特定的积分配置
 */
export const pathBasedCredits: RouteCreditConfig = {
  paths: {
    '/api/export/*': {
      GET: {
        cost: 25,
        deductionStrategy: 'pre',
        minimumBalance: 50,
        refundOnFailure: true,
      }
    },
    '/api/analytics/*': {
      GET: {
        cost: 10,
        deductionStrategy: 'post',
        refundOnFailure: true,
      }
    }
  }
};

/**
 * 使用示例：如何在现有路由构建器中应用积分中间件
 */
/*
// 方法 1: 直接使用中间件
const userApi = new HonoApiRouteBuilder(queryBuilder, 'users', '/api')
  .table('user')
  .primaryKey('id')
  .middleware('credits', createCreditMiddleware(standardCrudCredits));

// 方法 2: 使用增强版构建器
const enhancedUserApi = new EnhancedHonoApiRouteBuilder(queryBuilder, 'users', '/api')
  .table('user')
  .primaryKey('id')
  .withCredits(standardCrudCredits)
  .requireAuth({ required: true });

// 方法 3: 使用工厂函数
const quickUserApi = createStandardCrudApi(queryBuilder, 'users', {
  requireAuth: true,
  credits: { read: 1, write: 5, delete: 10 }
});
*/
