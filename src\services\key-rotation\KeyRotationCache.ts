import QuickLRU from 'quick-lru';
import type { Service, Key } from '../../app/schemas/key-rotation';
import type { DatabaseInstance } from '../../server/types';
import { eq, and, sql } from 'drizzle-orm';
import { services, keys } from '../../app/schemas/key-rotation';

interface CacheEntry<T> {
  data: T;
  expires_at: number;
}

/**
 * 密钥轮换缓存管理器
 * 使用内存缓存减少数据库查询压力
 */
export class KeyRotationCache {
  private cache: QuickLRU<string, CacheEntry<any>>;
  private defaultTTL: number = 30000; // 30秒默认TTL

  constructor(
    private db: DatabaseInstance,
    options: {
      maxSize?: number;
      defaultTTL?: number;
    } = {}
  ) {
    this.cache = new QuickLRU({
      maxSize: options.maxSize || 1000
    });
    this.defaultTTL = options.defaultTTL || 30000;
  }

  /**
   * 获取服务信息（带缓存）
   */
  async getService(serviceId: string): Promise<Service | null> {
    const cacheKey = `service:${serviceId}`;
    const cached = this.cache.get(cacheKey);

    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }

    // Cache miss，查询数据库
    const result = await this.db
      .select()
      .from(services)
      .where(eq(services.id, serviceId))
      .limit(1);

    const service = result[0] || null;

    if (service) {
      // 更新缓存
      this.cache.set(cacheKey, {
        data: service,
        expires_at: Date.now() + this.defaultTTL
      });
    }

    return service;
  }

  /**
   * 获取服务的活跃密钥（带缓存）
   */
  async getActiveKeys(serviceId: string): Promise<Key[]> {
    const cacheKey = `service:${serviceId}:active_keys`;
    const cached = this.cache.get(cacheKey);

    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }

    // Cache miss，查询数据库
    const result = await this.db
      .select()
      .from(keys)
      .where(
        and(
          eq(keys.service_id, serviceId),
          eq(keys.status, 'active'),
          sql`usage_count < max_usage`
        )
      )
      .orderBy(keys.usage_count);

    // 更新缓存，使用较短的TTL因为密钥状态变化频繁
    this.cache.set(cacheKey, {
      data: result,
      expires_at: Date.now() + (this.defaultTTL / 2) // 15秒TTL
    });

    return result;
  }

  /**
   * 获取密钥池状态（带缓存）
   */
  async getPoolStatus(serviceId: string): Promise<{
    total_keys: number;
    active_keys: number;
    exhausted_keys: number;
    invalid_keys: number;
    pool_health: 'healthy' | 'warning' | 'critical';
  }> {
    const cacheKey = `service:${serviceId}:pool_status`;
    const cached = this.cache.get(cacheKey);

    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }

    // 查询各种状态的密钥数量
    const [totalResult, activeResult, exhaustedResult, invalidResult] = await Promise.all([
      this.db.select({ count: sql<number>`count(*)` }).from(keys).where(eq(keys.service_id, serviceId)),
      this.db.select({ count: sql<number>`count(*)` }).from(keys).where(
        and(eq(keys.service_id, serviceId), eq(keys.status, 'active'), sql`usage_count < max_usage`)
      ),
      this.db.select({ count: sql<number>`count(*)` }).from(keys).where(
        and(eq(keys.service_id, serviceId), eq(keys.status, 'exhausted'))
      ),
      this.db.select({ count: sql<number>`count(*)` }).from(keys).where(
        and(eq(keys.service_id, serviceId), eq(keys.status, 'invalid'))
      )
    ]);

    const total_keys = totalResult[0]?.count || 0;
    const active_keys = activeResult[0]?.count || 0;
    const exhausted_keys = exhaustedResult[0]?.count || 0;
    const invalid_keys = invalidResult[0]?.count || 0;

    // 计算健康度
    let pool_health: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (total_keys === 0) {
      pool_health = 'critical';
    } else if (active_keys === 0) {
      pool_health = 'critical';
    } else if (active_keys / total_keys < 0.2) {
      pool_health = 'warning';
    }

    const status = {
      total_keys,
      active_keys,
      exhausted_keys,
      invalid_keys,
      pool_health
    };

    // 缓存状态信息，使用较短TTL
    this.cache.set(cacheKey, {
      data: status,
      expires_at: Date.now() + 10000 // 10秒TTL
    });

    return status;
  }

  /**
   * 缓存失效策略
   * 密钥使用后立即失效相关缓存
   */
  invalidateServiceCache(serviceId: string): void {
    const patterns = [
      `service:${serviceId}`,
      `service:${serviceId}:active_keys`,
      `service:${serviceId}:pool_status`
    ];

    for (const pattern of patterns) {
      this.cache.delete(pattern);
    }
  }

  /**
   * 批量失效缓存
   */
  invalidatePattern(pattern: string): void {
    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 检查缓存是否过期
   */
  private isCacheExpired(entry: CacheEntry<any>): boolean {
    return Date.now() > entry.expires_at;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.cache.maxSize
    };
  }

  /**
   * 手动设置缓存
   */
  set<T>(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      expires_at: Date.now() + (ttl || this.defaultTTL)
    });
  }

  /**
   * 手动获取缓存
   */
  get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }
    return null;
  }
}
