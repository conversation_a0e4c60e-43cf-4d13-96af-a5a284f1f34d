import type { SQLiteTableWithColumns } from "drizzle-orm/sqlite-core";
import { PAGINATION } from "../constants";
import type {
    SchemaConfig,
    ApiMiddleware,
    PaginationConfig,
    ApiContext,
    HttpMethod,
    RelationsConfig,
    TransactionCallback,
    FilterableField,
    DataTransformer,
    RequestInput,
    CustomMethodHandler,
    ProcessedApiContext
} from "../types";
import { QueryBuilder, createQueryBuilder } from "./query-builder";
import { MiddlewareManager, createMiddlewareManager } from "../core/middleware";
import { QueryUtils, ResponseUtils, ValidationUtils } from "../utils";
import { z } from "zod";
import type { SQL } from "drizzle-orm/sql";
import { eq, and, or, gt, gte, lt, lte, like, inArray, between, desc, asc, sql } from "drizzle-orm";

/**
 * 框架无关的 API 路由构建器基类
 * 包含所有配置方法和业务逻辑，子类只需实现框架特定的请求处理部分
 */
import type { AuthConfig } from "../auth/types";
import { AuthenticationMiddleware } from "../auth/AuthenticationMiddleware";

export abstract class BaseApiRouteBuilder {
    public resourceName: string = "";
    public tableDef!: SQLiteTableWithColumns<any>;
    public prefixPath: string = "";
    public schemas: SchemaConfig = {};
    public selectedFields: string[] = [];
    public orderByField?: string;
    public middlewares: ApiMiddleware[] = [];
    public searchableFields: string[] = [];
    public filterableFields: Record<string, FilterableField> = {};
    public sortableFields: string[] = [];
    public paramsFields: Record<string, z.ZodType<any>> = {};
    public paginationConfig: PaginationConfig = PAGINATION;
    public relationsConfig: RelationsConfig = {};
    public extrasConfig: Record<string, SQL.Aliased<unknown>> = {};
    protected uniqueOptions: {
        onConflict?: 'doNothing' | 'doUpdate' | 'throwError';
    } = { onConflict: 'doNothing' };
    protected queryBuilder: QueryBuilder;
    protected middlewareManager: MiddlewareManager = createMiddlewareManager();
    protected transactionCallback?: TransactionCallback<any>;
    protected primaryKeyParam: string = "id"; // 默认主键参数名
    protected transformFunction: DataTransformer = async (method, data, db, apiContext) => data;
    protected authConfig?: AuthConfig;
    protected customWhereFunction?: (
        table: SQLiteTableWithColumns<any>,
        context: ApiContext,
        helpers: any
    ) => SQL<unknown> | null;

    // Custom method handlers
    protected customGetHandler?: CustomMethodHandler;
    protected customPostHandler?: CustomMethodHandler;
    protected customPutHandler?: CustomMethodHandler;
    protected customDeleteHandler?: CustomMethodHandler;

    constructor() {
        this.queryBuilder = createQueryBuilder();
    }

    resource(name: string, table: SQLiteTableWithColumns<any>) {
        this.resourceName = name;
        this.tableDef = table;
        return this;
    }

    // 配置额外字段
    extras(extras: Record<string, SQL.Aliased<unknown>>) {
        this.extrasConfig = extras;
        return this;
    }

    prefix(path: string) {
        this.prefixPath = path;
        return this;
    }

    schema(schemas: SchemaConfig) {
        this.schemas = schemas;
        return this;
    }

    select(fields: string[]) {
        this.selectedFields = fields;
        return this;
    }

    orderBy(field: string) {
        this.orderByField = field;
        return this;
    }

    searchable(fields: string[]) {
        this.searchableFields = fields;
        return this;
    }

    /**
     * Configure filterable fields with their schemas and allowed operators
     * @example
     * .filterable({
     *   name: { schema: z.string() }, // Default eq operator only
     *   age: { schema: z.number(), operators: ['eq', 'gt', 'lt'] },
     *   status: { schema: z.enum(['active', 'inactive']), operators: ['eq', 'in'] },
     *   calculated_field: { schema: z.number(), operators: ['eq', 'gt', 'lt'], isExtras: true }
     * })
     */
    filterable(fields: Record<string, FilterableField>) {
        // 为每个字段的 schema 添加 transform 处理
        this.filterableFields = Object.entries(fields).reduce(
            (acc, [field, config]) => {
                // 如果是字符串类型的 schema，添加 transform 来处理数字
                if (config.schema instanceof z.ZodString) {
                    acc[field] = {
                        ...config,
                        schema: config.schema.transform((val) => String(val)),
                    };
                } else if (config.schema instanceof z.ZodNumber) {
                    // 检查是否包含between操作符
                    const hasBetweenOperator = config.operators?.includes("between");

                    if (hasBetweenOperator) {
                        // 对于包含between操作符的数字字段，使用自定义的预处理函数
                        acc[field] = {
                            ...config,
                            schema: z.preprocess((val) => {
                                // 如果值是字符串且包含逗号（可能是between操作符的值）
                                if (typeof val === 'string' && val.includes(',')) {
                                    // 将值保留为字符串，在查询构建器中再处理
                                    return val;
                                }
                                // 否则尝试转换为数字
                                return val === "" ? undefined : Number(val);
                            }, config.schema),
                        };
                    } else {
                        // 对于普通数字字段，直接转换
                        acc[field] = {
                            ...config,
                            schema: z.preprocess(
                                (val) => val === "" ? undefined : Number(val),
                                config.schema
                            ),
                        };
                    }
                } else {
                    acc[field] = config;
                }
                return acc;
            },
            {} as Record<string, FilterableField>
        );

        return this;
    }

    params(params: Record<string, z.ZodType<any>>) {
        this.paramsFields = params;
        return this;
    }

    sortable(fields: string[]) {
        this.sortableFields = fields;
        return this;
    }

    /**
     * 发送数据到前端前的数据转换
     * @param data 原始数据
     */
    transform(transformFunction: DataTransformer) {
        this.transformFunction = transformFunction;
        return this;
    }

    /**
     * Configure pagination settings
     * @example
     * .pagination({
     *   defaultLimit: 20,  // Default items per page
     *   maxLimit: 100     // Maximum allowed items per page
     * })
     */
    pagination(config: Partial<PaginationConfig>) {
        this.paginationConfig = { ...PAGINATION, ...config };
        return this;
    }

    middleware(middlewares: ApiMiddleware[] | ApiMiddleware, middlewareId: string) {
        const mwArray = Array.isArray(middlewares) ? middlewares : [middlewares];
        mwArray.forEach((mw) => this.middlewareManager.use(mw, middlewareId));
        return this;
    }

    unique(options?: {
        onConflict?: 'doNothing' | 'doUpdate' | 'throwError';
    }) {
        this.uniqueOptions = { ...this.uniqueOptions, ...options };
        return this;
    }

    /**
     * Configure relations to be loaded with the query
     * @example
     * .relations({
     *   author: true,
     *   comments: {
     *     limit: 5,
     *     orderBy: (comments, { desc }) => [desc(comments.createdAt)]
     *   }
     * })
     */
    relations(config: RelationsConfig) {
        this.relationsConfig = config;
        return this;
    }

    transaction<T>(callback: TransactionCallback<T>) {
        this.transactionCallback = callback;
        return this;
    }

    primaryKey(paramName: string) {
        this.primaryKeyParam = paramName;
        return this;
    }

    /**
     * 获取主键参数名（用于外部访问）
     */
    getPrimaryKeyParam(): string {
        return this.primaryKeyParam;
    }

    // Configure authentication (optional; convention-first even without calling this)
    auth(config?: AuthConfig) {
        this.authConfig = config;

        // Create and register authentication middleware
        const authMiddleware = new AuthenticationMiddleware(config);
        this.middleware(authMiddleware.getMiddleware(), 'authentication');

        return this;
    }

    // HTTP method override methods
    get(handler?: CustomMethodHandler) {
        this.customGetHandler = handler;
        return this;
    }

    post(handler?: CustomMethodHandler) {
        this.customPostHandler = handler;
        return this;
    }

    put(handler?: CustomMethodHandler) {
        this.customPutHandler = handler;
        return this;
    }

    delete(handler?: CustomMethodHandler) {
        this.customDeleteHandler = handler;
        return this;
    }

    /**
     * 验证路由参数
     */
    protected validateRouteParams(params: Record<string, string>) {
        return ValidationUtils.validateRouteParams(params || {}, this.paramsFields);
    }

    /**
     * 验证请求体
     */
    protected async validateRequestBody(input: RequestInput, method: HttpMethod) {
        let validatedBody = {};
        let originalBody = {};

        if (['POST', 'PUT'].includes(method)) {
            try {
                originalBody = await input.getBody();

                const schema = this.schemas?.[method as 'POST' | 'PUT'];
                if (schema) {
                    const validationResult = await schema.safeParseAsync(originalBody);
                    if (!validationResult.success) {
                        return ResponseUtils.badRequest('Validation failed', {
                            errors: (validationResult as any).error?.errors ?? []
                        });
                    }
                    validatedBody = validationResult.data as any;
                } else {
                    validatedBody = originalBody;
                }
            } catch (error) {
                if (['POST', 'PUT'].includes(method)) {
                    return ResponseUtils.badRequest('Invalid JSON body');
                }
            }
        } else if (method === 'DELETE') {
            try {
                originalBody = await input.getBody();
                if (originalBody && typeof originalBody === 'object' && 'batch' in originalBody) {
                    validatedBody = originalBody;
                }
            } catch (error) {
                validatedBody = {};
                originalBody = {};
            }
        }

        return { body: validatedBody, originalBody };
    }

    /**
     * 验证查询参数
     */
    protected validateQueryParams(searchParams: URLSearchParams) {
        const parsedQuery = QueryUtils.parseAllQueryParams(
            searchParams,
            this.filterableFields,
            Object.keys(this.paramsFields)
        );

        const validationResult = ValidationUtils.validateQueryParams(
            {
                search: parsedQuery.search,
                filter: parsedQuery.filter || {},
                sortFields: parsedQuery.sortFields,
                pagination: parsedQuery.pagination,
            },
            {
                filterableFields: this.filterableFields,
                sortableFields: this.sortableFields,
                pagination: this.paginationConfig
            }
        );

        if (validationResult instanceof Response) {
            return validationResult;
        }

        return parsedQuery;
    }

    /**
     * 构建 API 上下文
     */
    protected buildApiContext(
        method: HttpMethod,
        validatedParams: any,
        validatedBody: any,
        originalBody: any,
        queryValidation: any,
        searchParams: URLSearchParams
    ): ApiContext {
        return {
            method,
            params: validatedParams,
            query: {
                search: queryValidation.search,
                filter: queryValidation.filter || {},
                sort: queryValidation.sortFields?.length > 0
                    ? queryValidation.sortFields.map((s: any) => `${s.field}:${s.order}`).join(",")
                    : undefined,
                page: queryValidation.pagination.page || 1,
                limit: Math.min(
                    queryValidation.pagination.limit || this.paginationConfig.defaultLimit,
                    this.paginationConfig.maxLimit
                ),
            },
            originalQuery: searchParams,
            originalBody: originalBody,
            body: method === "POST"
                ? { ...(validatedParams as any), ...(validatedBody as any) }
                : (validatedBody as any),
        };
    }

    /**
     * 应用约定配置（包括认证）
     */
    protected applyConventions() {
        // 约定优先：如果没有显式配置认证，则应用默认认证规则
        if (!this.middlewareManager.hasMiddleware('authentication')) {
            const authConfig = this.deriveAuthConfigFromConventions();
            if (authConfig) {
                this.auth(authConfig);
            }
        }
    }

    /**
     * 根据约定推导认证配置
     */
    protected deriveAuthConfigFromConventions(): AuthConfig | null {
        // 基本约定：GET 公开，其他需要认证
        // 如果资源名或路径包含 admin，需要 admin 角色
        const isAdminResource = this.resourceName.includes('admin') || this.prefixPath.includes('/admin/');

        if (isAdminResource) {
            return {
                strategy: 'auto',
                globalAuth: { required: true, roles: ['admin'] }
            };
        }

        // 普通资源：GET 公开，其他需要认证
        return {
            strategy: 'auto',
            routes: {
                'GET': { required: false },
                'POST': { required: true },
                'PUT': { required: true },
                'PATCH': { required: true },
                'DELETE': { required: true }
            }
        };
    }

    /**
     * 注册核心请求处理中间件
     */
    protected registerCoreRequestMiddleware(method: HttpMethod, apiContext: ApiContext) {
        const coreMiddleware = async (ctx: any, next: any) => {
            const result = await this.executeQuery(method, apiContext)
                .then((res) => ResponseUtils.success(res))
                .catch((err) => err);
            ctx.response = result;
            await next();
        };

        this.middlewareManager.use(coreMiddleware, "core-request-middleware");
    }

    /**
     * 创建中间件上下文
     */
    protected createMiddlewareContext(method: HttpMethod, pathname: string, headers: Headers, req?: any) {
        // 创建基础的中间件上下文
        const middlewareContext = {
            req: req || null, // 框架特定的请求对象
            method,
            path: pathname,
            headers,
            state: new Map(),
            response: undefined as Response | undefined,
            error: undefined as Error | undefined,
            middlewareManager: this.middlewareManager
        };
        return middlewareContext;
    }

    /**
     * 通用请求处理方法 - 框架无关的核心逻辑
     * 子类通过实现 RequestInput 接口来适配不同框架
     */
    protected async processRequest(input: RequestInput): Promise<Response> {
        try {
            const url = new URL(input.url);
            const method = input.method;

            // 1. 验证路由参数
            const validatedParams = this.validateRouteParams(input.params);
            if (validatedParams instanceof Response) return validatedParams;

            // 2. 解析和验证请求体
            const bodyValidation = await this.validateRequestBody(input, method);
            if (bodyValidation instanceof Response) return bodyValidation;

            // 3. 解析和验证查询参数
            const queryValidation = this.validateQueryParams(input.searchParams);
            if (queryValidation instanceof Response) return queryValidation;

            // 4. 构建 API 上下文
            const apiContext = this.buildApiContext(
                method,
                validatedParams,
                bodyValidation.body,
                bodyValidation.originalBody,
                queryValidation,
                input.searchParams
            );

            // 5. 应用约定（包括认证）
            this.applyConventions();

            // 6. 注册核心处理中间件
            this.registerCoreRequestMiddleware(method, apiContext);

            // 7. 构建中间件上下文
            const middlewareContext = this.createMiddlewareContext(method, url.pathname, input.headers);
            middlewareContext.state.set("resourceName", this.resourceName);

            // 8. 执行中间件链
            await this.middlewareManager.execute(middlewareContext);

            return middlewareContext.response || ResponseUtils.internalError("No response generated");

        } catch (error) {
            if (error instanceof Response) return error;
            return ResponseUtils.internalError(
                error instanceof Error ? error.message : "Internal server error"
            );
        }
    }

    /**
     * Add a custom WHERE clause with access to request context
     * @example
     * .where((items, context, { eq, inArray, db }) => {
     *   // Only apply filter if 'meta_keyword' parameter is present
     *   if (context.originalQuery.get("meta_keyword")) {
     *     const subquery = db.select({ id: someTable.id })
     *       .from(someTable)
     *       .where(eq(someTable.meta_keyword, context.originalQuery.get("meta_keyword")));
     *     return inArray(items.id, subquery);
     *   }
     *   return null; // No additional filter needed
     * })
     */
    where(whereFunction: (
        table: SQLiteTableWithColumns<any>,
        context: ApiContext,
        helpers: any
    ) => SQL<unknown> | null) {
        this.customWhereFunction = whereFunction;
        return this;
    }

    /**
     * 执行批量操作
     */
    protected async executeBatchOperation(
        method: HttpMethod,
        context: ApiContext
    ) {
        const { body } = context;

        // 确保body是数组
        if (!Array.isArray(body.batch)) {
            throw ResponseUtils.badRequest('Batch operation requires an array of records');
        }

        // 验证每条记录（根据不同的方法使用不同的 schema）
        if (method === "POST" && this.schemas?.POST) {
            const validationResult = await this.schemas!.POST!.safeParseAsync(body);
            const errors = (validationResult as any).error?.errors ?? [];
            if (errors.length) {
                throw ResponseUtils.badRequest('Validation failed', { errors });
            }
        } else if (method === "PUT" && this.schemas?.PUT) {
            const validationResult = await this.schemas!.PUT!.safeParseAsync(body);
            const errors = (validationResult as any).error?.errors ?? [];
            if (errors.length) {
                throw ResponseUtils.badRequest('Validation failed', { errors });
            }
        }
        // DELETE 操作的批量删除通常只需要 ID 数组，不需要 schema 验证

        switch (method) {
            case "POST": {
                return this.queryBuilder.buildBatchInsertQuery(
                    this.tableDef,
                    body.batch,
                    {
                        batchSize: 5,
                        ...this.uniqueOptions
                    }
                );
            }

            case "PUT": {
                return this.queryBuilder.buildBatchUpdateQuery(
                    this.tableDef,
                    body.batch,
                    { batchSize: 100 }
                );
            }

            case "DELETE": {
                return this.queryBuilder.buildBatchDeleteQuery(
                    this.tableDef,
                    body.batch
                );
            }
            default:
                throw ResponseUtils.methodNotAllowed(`Batch operation not supported for method: ${method}`);
        }
    }

    /**
     * 获取自定义方法处理器
     */
    private getCustomHandler(method: HttpMethod): CustomMethodHandler | null {
        switch (method) {
            case 'GET': return this.customGetHandler || null;
            case 'POST': return this.customPostHandler || null;
            case 'PUT': return this.customPutHandler || null;
            case 'DELETE': return this.customDeleteHandler || null;
            default: return null;
        }
    }

    /**
     * 创建处理后的API上下文
     */
    private createProcessedContext(context: ApiContext): ProcessedApiContext {
        return {
            method: context.method,
            params: context.params,
            body: context.body,
            query: context.query,
            originalQuery: context.originalQuery,
            originalBody: context.originalBody,
            // auth信息会在中间件执行后注入
        };
    }

    /**
     * 执行查询操作 - 框架无关的核心业务逻辑
     */
    protected async executeQuery(method: HttpMethod, context: ApiContext) {
        // 1. 检查是否有自定义方法处理器
        const customHandler = this.getCustomHandler(method);

        if (customHandler) {
            // 执行自定义处理器
            const processedContext = this.createProcessedContext(context);
            return await customHandler(processedContext);
        }

        // 2. If transaction callback is provided, use it
        if (this.transactionCallback) {
            const result = await this.queryBuilder.transaction(async (tx) => {
                // For custom transaction, ignore the method and execute callback directly
                const result = await this.transactionCallback!(tx, context).catch(error => {
                    console.log("errorsss", error)
                    return null
                })
                return { data: result };
            });
            if (result.data !== null) return result
        }

        // 3. 执行默认CRUD逻辑（没有自定义处理器时）
        switch (method) {
            case "GET": {
                const { search, filter, sort, page, limit } = context.query;

                // Build base where clause from query filters
                const whereClause = QueryUtils.buildFilterConditions(
                    Object.keys(this.filterableFields),
                    {
                        ...(filter || {}),
                        ...Object.entries(context.params).reduce(
                            (acc, [key, value]) => ({ ...acc, [key]: { eq: value } }),
                            {}
                        ),
                    }
                );

                if (search) {
                    whereClause["_search"] = QueryUtils.buildSearchCondition(
                        this.searchableFields,
                        search
                    );
                }

                // Generate custom WHERE clause if function is provided
                let customWhere: SQL<unknown> | undefined;
                if (this.customWhereFunction) {
                    // Provide helpers object with SQL operations and useful context
                    const helpers = {
                        // SQL operators
                        eq, and, or, gt, gte, lt, lte, like, inArray, between, desc, asc,
                        // Database access
                        db: this.queryBuilder.db,
                        // Utility functions
                        SQL: sql, // Allow raw SQL if needed
                        isFilterPresent: (field: string) =>
                            !!filter && Object.prototype.hasOwnProperty.call(filter, field)
                    };

                    const customWhereResult = this.customWhereFunction(this.tableDef, context, helpers);
                    if (customWhereResult !== null) {
                        customWhere = customWhereResult;
                    }
                }
                // Handle query with common options
                const queryOptions = {
                    select: this.selectedFields,
                    where: context.params[this.primaryKeyParam]
                        ? { ...whereClause, id: context.params[this.primaryKeyParam] }
                        : whereClause,
                    customWhere, // Pass the custom WHERE clause
                    with: this.relationsConfig,
                    extras: this.extrasConfig,
                    detail: !!context.params[this.primaryKeyParam],
                };

                // For detail query, just return single result
                if (context.params[this.primaryKeyParam]) {
                    const result = await this.queryBuilder.buildSelectQuery(
                        this.tableDef,
                        this.resourceName,
                        queryOptions
                    ).catch((err) => {
                        if (err instanceof Response) throw err;
                        throw ResponseUtils.internalError(err.message);
                    });
                    if (!result) {
                        throw ResponseUtils.notFound(
                            `Resource with ${this.primaryKeyParam}=${context.params[this.primaryKeyParam]
                            } not found`
                        );
                    }
                    return result;
                }

                // Parse and validate sort fields
                const sortFields = QueryUtils.parseSortFields(sort || this.orderByField);
                if (sortFields.length > 0) {
                    const invalidFields = sortFields
                        .map(({ field }) => field)
                        .filter((field) => !this.sortableFields.includes(field));

                    if (invalidFields.length > 0) {
                        throw ResponseUtils.badRequest(
                            `Invalid sort fields: ${invalidFields.join(", ")}. ` +
                            `Allowed fields: ${this.sortableFields.join(", ")}`
                        );
                    }
                }

                // For list query, add pagination and return with total
                const [total, result] = await Promise.all([
                    this.queryBuilder.getCount(this.tableDef, whereClause, customWhere, this.extrasConfig),
                    this.queryBuilder.buildSelectQuery(this.tableDef, this.resourceName, {
                        ...queryOptions,
                        orderBy: sortFields,
                        limit: Math.min(
                            limit || this.paginationConfig.defaultLimit,
                            this.paginationConfig.maxLimit
                        ),
                        offset:
                            ((page || 1) - 1) * (limit || this.paginationConfig.defaultLimit),
                    }),
                ]).catch((err: any) => {
                    if (err instanceof Response) throw err;
                    throw ResponseUtils.internalError(err.message);
                });

                const transformedResult = await this.transformFunction(method, result, this.queryBuilder.db, context);

                return {
                    data: transformedResult,
                    total,
                    page: page || 1,
                    limit: limit || this.paginationConfig.defaultLimit,
                    hasMore:
                        result.length === (limit || this.paginationConfig.defaultLimit),
                };
            }

            case "POST": {
                const result = await this.queryBuilder
                    .buildInsertQuery(this.tableDef, context.body, this.uniqueOptions)
                    .catch((err) => {
                        if (err instanceof Response) throw err;
                        throw ResponseUtils.internalError(err.message);
                    });
                const transformedResult = await this.transformFunction(method, result[0], this.queryBuilder.db, context);
                return transformedResult;
            }

            case "PUT": {
                if (!context.params[this.primaryKeyParam]) {
                    throw ResponseUtils.badRequest(
                        `${this.primaryKeyParam} is required for PUT operation`
                    );
                }

                const result = await this.queryBuilder
                    .buildUpdateQuery(
                        this.tableDef,
                        context.params[this.primaryKeyParam]!,
                        context.body,
                    )
                    .catch((err) => {
                        if (err instanceof Response) throw err;
                        throw ResponseUtils.internalError(err.message);
                    });
                if (!result[0]) {
                    throw ResponseUtils.notFound(
                        `Resource with ${this.primaryKeyParam}=${context.params[this.primaryKeyParam]
                        } not found`
                    );
                }
                const transformedResult = await this.transformFunction(method, result[0], this.queryBuilder.db, context);
                return transformedResult;
            }

            case "DELETE": {
                if (!context.params[this.primaryKeyParam]) {
                    throw ResponseUtils.badRequest(
                        `${this.primaryKeyParam} is required for DELETE operation`
                    );
                }

                const result = await this.queryBuilder
                    .buildDeleteQuery(this.tableDef, context.params[this.primaryKeyParam]!)
                    .catch((err) => {
                        if (err instanceof Response) throw err;
                        throw ResponseUtils.internalError(err.message);
                    });
                const transformedResult = await this.transformFunction(method, result[0], this.queryBuilder.db, context);
                return transformedResult;
            }

            default:
                throw ResponseUtils.methodNotAllowed(`Unsupported method: ${method}`);
        }
    }

    /**
     * 抽象方法 - 子类必须实现的框架特定方法
     */
    abstract build(): any;
} 