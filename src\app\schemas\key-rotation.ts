import { sql } from 'drizzle-orm';
import { 
  sqliteTable, 
  text, 
  integer, 
  real,
  index
} from 'drizzle-orm/sqlite-core';

// 服务配置类型定义
export interface ServiceConfig {
  rotation_strategy: {
    max_usage: number;
    selection_algorithm: 'round_robin' | 'random' | 'weighted' | 'least_used';
  };
  pool_config: {
    min_active_keys: number;
    auto_disable_failed: boolean;
  };
  cache_config: {
    enable_cache: boolean;
    cache_ttl_seconds: number;
  };
}

// 密钥状态类型
export type KeyStatus = 'active' | 'inactive' | 'exhausted' | 'invalid';

// 服务表
export const services = sqliteTable('services', {
  id: text('id').primaryKey(),
  name: text('name').notNull().unique(),
  description: text('description'),
  config: text('config', { mode: 'json' }).notNull().$type<ServiceConfig>(),
  is_active: integer('is_active', { mode: 'boolean' }).notNull().default(true),
  created_at: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  updated_at: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// 密钥表
export const keys = sqliteTable('keys', {
  id: text('id').primaryKey(),
  service_id: text('service_id').notNull().references(() => services.id, { onDelete: 'cascade' }),
  key_value: text('key_value').notNull(), // 加密存储
  status: text('status').notNull().$type<KeyStatus>().default('active'),
  usage_count: integer('usage_count').notNull().default(0),
  max_usage: integer('max_usage').notNull(),
  priority: real('priority').notNull().default(1.0),
  created_at: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
  last_used_at: text('last_used_at')
}, (table) => ({
  // 性能关键索引
  serviceStatusUsageIdx: index('idx_keys_service_status_usage')
    .on(table.service_id, table.status, table.usage_count),
  lastUsedIdx: index('idx_keys_last_used').on(table.last_used_at),
}));

// 使用日志表
export const usageLogs = sqliteTable('usage_logs', {
  id: text('id').primaryKey(),
  key_id: text('key_id').notNull().references(() => keys.id, { onDelete: 'cascade' }),
  service_id: text('service_id').notNull().references(() => services.id, { onDelete: 'cascade' }),
  timestamp: text('timestamp').notNull().default(sql`CURRENT_TIMESTAMP`),
  metadata: text('metadata', { mode: 'json' })
}, (table) => ({
  serviceTimeIdx: index('idx_usage_logs_service_time').on(table.service_id, table.timestamp),
}));

// 导出类型
export type Service = typeof services.$inferSelect;
export type NewService = typeof services.$inferInsert;
export type Key = typeof keys.$inferSelect;
export type NewKey = typeof keys.$inferInsert;
export type UsageLog = typeof usageLogs.$inferSelect;
export type NewUsageLog = typeof usageLogs.$inferInsert;
