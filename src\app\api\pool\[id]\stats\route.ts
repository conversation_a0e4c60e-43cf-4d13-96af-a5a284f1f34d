/**
 * 服务统计 API
 * 路径: /api/services/[id]/stats
 * 支持的方法: GET
 */

import type { Context } from 'hono';
import { keyRotationServices } from '@/services/key-rotation';

/**
 * GET /api/services/[id]/stats - 获取服务统计信息
 */
export async function GET(c: Context) {
  try {
    const serviceId = c.req.param('id');
    const stats = await keyRotationServices.serviceManager.getServiceStats(serviceId);
    
    if (!stats.service) {
      return c.json({
        success: false,
        error: 'Service not found'
      }, 404);
    }
    
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get service stats error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get service stats'
    }, 500);
  }
}
