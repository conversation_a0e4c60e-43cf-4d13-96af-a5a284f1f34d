/**
 * 服务密钥管理 API
 * 路径: /api/services/[id]/keys
 * 支持的方法: GET, POST
 */

import type { Context } from 'hono';
import { HonoApiRouteBuilder } from '@/server/builders/hono-route-builder';
import { keys } from '@/app/schemas/key-rotation';
import { CreateKeySchema, BatchCreateKeysSchema } from '@/app/types/key-rotation';
import { keyRotationServices } from '@/services/key-rotation';

// 创建密钥管理路由构建器
const keyRoutes = new HonoApiRouteBuilder()
  .table(keys)
  .primaryKey('id')
  .schema({
    create: CreateKeySchema
  })
  .customHandler('list', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const query = c.req.query();
      
      const options = {
        status: query.status,
        limit: query.limit ? parseInt(query.limit) : undefined,
        offset: query.offset ? parseInt(query.offset) : undefined
      };
      
      const keys = await keyRotationServices.keyManager.getServiceKeys(serviceId, options);
      
      return c.json({
        success: true,
        data: keys,
        pagination: {
          limit: options.limit,
          offset: options.offset,
          total: keys.length
        }
      });
    } catch (error) {
      console.error('List keys error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list keys'
      }, 500);
    }
  })
  .customHandler('create', async (c: Context) => {
    try {
      const serviceId = c.req.param('id');
      const body = await c.req.json();
      
      // 检查是否是批量创建
      if (Array.isArray(body.keys)) {
        const validatedData = BatchCreateKeysSchema.parse(body);
        const keys = await keyRotationServices.keyManager.addKeys(serviceId, validatedData.keys);
        
        return c.json({
          success: true,
          data: keys,
          message: `Successfully created ${keys.length} keys`
        }, 201);
      } else {
        const validatedData = CreateKeySchema.parse(body);
        const keys = await keyRotationServices.keyManager.addKeys(serviceId, [validatedData]);
        
        return c.json({
          success: true,
          data: keys[0]
        }, 201);
      }
    } catch (error) {
      console.error('Create keys error:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create keys'
      }, 400);
    }
  })
  .build();

/**
 * GET /api/services/[id]/keys - 获取服务的密钥列表
 */
export async function GET(c: Context) {
  return keyRoutes.GET(c);
}

/**
 * POST /api/services/[id]/keys - 为服务添加密钥（支持单个或批量）
 */
export async function POST(c: Context) {
  return keyRoutes.POST(c);
}
